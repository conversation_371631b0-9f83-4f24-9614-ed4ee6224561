import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/grocery_item.dart';
import '../models/grocery_category.dart';
import '../providers/grocery_provider.dart';
import '../widgets/add_item_dialog.dart';
import '../utils/constants.dart';

class GroceryTile extends StatelessWidget {
  final GroceryItem item;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const GroceryTile({
    super.key,
    required this.item,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding / 2,
      ),
      elevation: 2,
      color: AppConstants.cardColor,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        leading: Checkbox(
          value: item.isChecked,
          onChanged: (bool? value) {
            context.read<GroceryProvider>().toggleItemCheck(item);
          },
          activeColor: AppConstants.primaryColor,
        ),
        title: Text(
          item.quantityDisplay,
          style: item.isChecked
              ? AppConstants.checkedTextStyle
              : AppConstants.bodyTextStyle,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category and frequency info
            Row(
              children: [
                // Category indicator
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: GroceryCategories.getCategoryById(item.category).color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        GroceryCategories.getCategoryById(item.category).icon,
                        size: 12,
                        color: GroceryCategories.getCategoryById(item.category).color,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        GroceryCategories.getCategoryById(item.category).name,
                        style: TextStyle(
                          fontSize: 10,
                          color: GroceryCategories.getCategoryById(item.category).color,
                          fontWeight: FontWeight.bold,
                          decoration: item.isChecked ? TextDecoration.lineThrough : null,
                        ),
                      ),
                    ],
                  ),
                ),
                if (item.frequency > 1) ...[
                  const SizedBox(width: 8),
                  Text(
                    '${item.frequency}x bought',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppConstants.secondaryTextColor,
                      decoration: item.isChecked ? TextDecoration.lineThrough : null,
                    ),
                  ),
                ],
              ],
            ),
            // Price and notes
            if (item.price != null || item.notes != null) ...[
              const SizedBox(height: 2),
              Row(
                children: [
                  if (item.price != null) ...[
                    Text(
                      item.totalPriceDisplay,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                        decoration: item.isChecked ? TextDecoration.lineThrough : null,
                      ),
                    ),
                    if (item.notes != null) const SizedBox(width: 8),
                  ],
                  if (item.notes != null)
                    Expanded(
                      child: Text(
                        item.notes!,
                        style: TextStyle(
                          fontSize: 11,
                          color: AppConstants.secondaryTextColor,
                          fontStyle: FontStyle.italic,
                          decoration: item.isChecked ? TextDecoration.lineThrough : null,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.isChecked)
              Icon(
                Icons.check_circle,
                color: AppConstants.primaryColor,
              )
            else
              IconButton(
                icon: Icon(
                  Icons.edit,
                  color: AppConstants.secondaryTextColor,
                  size: 20,
                ),
                onPressed: () {
                  _showEditDialog(context);
                },
                tooltip: 'Edit item',
              ),
          ],
        ),
        onTap: onTap ??
            () {
              context.read<GroceryProvider>().toggleItemCheck(item);
            },
        onLongPress: onLongPress ??
            () {
              _showDeleteDialog(context);
            },
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(AppConstants.confirmDeleteTitle),
          content: Text(
            '${AppConstants.confirmDeleteMessage}\n\n"${item.name}"',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                context.read<GroceryProvider>().removeItem(item);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(AppConstants.itemRemovedMessage),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AddItemDialog(editItem: item);
      },
    );
  }
}
