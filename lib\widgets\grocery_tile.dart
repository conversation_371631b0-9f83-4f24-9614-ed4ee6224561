import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/grocery_item.dart';
import '../providers/grocery_provider.dart';
import '../utils/constants.dart';

class GroceryTile extends StatelessWidget {
  final GroceryItem item;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const GroceryTile({
    super.key,
    required this.item,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding / 2,
      ),
      elevation: 2,
      color: AppConstants.cardColor,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        leading: Checkbox(
          value: item.isChecked,
          onChanged: (bool? value) {
            context.read<GroceryProvider>().toggleItemCheck(item);
          },
          activeColor: AppConstants.primaryColor,
        ),
        title: Text(
          item.name,
          style: item.isChecked
              ? AppConstants.checkedTextStyle
              : AppConstants.bodyTextStyle,
        ),
        subtitle: item.frequency > 1
            ? Text(
                'Added ${item.frequency} times',
                style: TextStyle(
                  fontSize: 12,
                  color: AppConstants.secondaryTextColor,
                  decoration: item.isChecked ? TextDecoration.lineThrough : null,
                ),
              )
            : null,
        trailing: item.isChecked
            ? Icon(
                Icons.check_circle,
                color: AppConstants.primaryColor,
              )
            : null,
        onTap: onTap ??
            () {
              context.read<GroceryProvider>().toggleItemCheck(item);
            },
        onLongPress: onLongPress ??
            () {
              _showDeleteDialog(context);
            },
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(AppConstants.confirmDeleteTitle),
          content: Text(
            '${AppConstants.confirmDeleteMessage}\n\n"${item.name}"',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                context.read<GroceryProvider>().removeItem(item);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(AppConstants.itemRemovedMessage),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }
}
