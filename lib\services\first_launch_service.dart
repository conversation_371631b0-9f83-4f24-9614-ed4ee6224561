import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class FirstLaunchService {
  static FirstLaunchService? _instance;
  static FirstLaunchService get instance => _instance ??= FirstLaunchService._();
  
  FirstLaunchService._();

  static const String _firstLaunchKey = 'is_first_launch';
  static const String _sampleDataAddedKey = 'sample_data_added';
  static const String _appVersionKey = 'app_version';
  
  SharedPreferences? _prefs;

  /// Initialize the service
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Check if this is the first time the app is launched
  Future<bool> isFirstLaunch() async {
    if (_prefs == null) {
      await init();
    }
    
    final isFirstLaunch = _prefs!.getBool(_firstLaunchKey) ?? true;
    debugPrint('FirstLaunchService: isFirstLaunch = $isFirstLaunch');
    return isFirstLaunch;
  }

  /// Mark that the app has been launched before
  Future<void> markFirstLaunchComplete() async {
    if (_prefs == null) {
      await init();
    }
    
    await _prefs!.setBool(_firstLaunchKey, false);
    debugPrint('FirstLaunchService: First launch marked as complete');
  }

  /// Check if sample data has been added
  Future<bool> hasSampleDataBeenAdded() async {
    if (_prefs == null) {
      await init();
    }
    
    return _prefs!.getBool(_sampleDataAddedKey) ?? false;
  }

  /// Mark that sample data has been added
  Future<void> markSampleDataAdded() async {
    if (_prefs == null) {
      await init();
    }
    
    await _prefs!.setBool(_sampleDataAddedKey, true);
    debugPrint('FirstLaunchService: Sample data marked as added');
  }

  /// Reset first launch status (useful for testing)
  Future<void> resetFirstLaunchStatus() async {
    if (_prefs == null) {
      await init();
    }
    
    await _prefs!.remove(_firstLaunchKey);
    await _prefs!.remove(_sampleDataAddedKey);
    debugPrint('FirstLaunchService: First launch status reset');
  }

  /// Get the stored app version
  Future<String?> getStoredAppVersion() async {
    if (_prefs == null) {
      await init();
    }
    
    return _prefs!.getString(_appVersionKey);
  }

  /// Store the current app version
  Future<void> storeAppVersion(String version) async {
    if (_prefs == null) {
      await init();
    }
    
    await _prefs!.setString(_appVersionKey, version);
  }

  /// Check if this is a fresh install or app update
  Future<bool> isNewInstallOrUpdate(String currentVersion) async {
    final storedVersion = await getStoredAppVersion();
    
    if (storedVersion == null) {
      // Fresh install
      await storeAppVersion(currentVersion);
      return true;
    }
    
    if (storedVersion != currentVersion) {
      // App update
      await storeAppVersion(currentVersion);
      return true;
    }
    
    return false;
  }

  /// Clear all stored preferences (for complete reset)
  Future<void> clearAll() async {
    if (_prefs == null) {
      await init();
    }
    
    await _prefs!.clear();
    debugPrint('FirstLaunchService: All preferences cleared');
  }
}
