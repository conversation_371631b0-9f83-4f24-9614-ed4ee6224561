import 'package:flutter/material.dart';
import '../services/theme_service.dart';

class ThemeProvider extends ChangeNotifier {
  final ThemeService _themeService = ThemeService.instance;
  
  ThemeMode _themeMode = ThemeMode.system;
  Color _primaryColor = Colors.green;
  
  ThemeMode get themeMode => _themeMode;
  Color get primaryColor => _primaryColor;
  
  ThemeData get lightTheme => ThemeService.getLightTheme(_primaryColor);
  ThemeData get darkTheme => ThemeService.getDarkTheme(_primaryColor);

  /// Initialize the provider
  Future<void> init() async {
    await _themeService.init();
    _themeMode = await _themeService.getThemeMode();
    _primaryColor = await _themeService.getPrimaryColor();
    notifyListeners();
  }

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    await _themeService.setThemeMode(mode);
    notifyListeners();
  }

  /// Set primary color
  Future<void> setPrimaryColor(Color color) async {
    _primaryColor = color;
    await _themeService.setPrimaryColor(color);
    notifyListeners();
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final newMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    await setThemeMode(newMode);
  }

  /// Check if dark mode is enabled
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  
  /// Check if system theme is enabled
  bool get isSystemTheme => _themeMode == ThemeMode.system;
}
