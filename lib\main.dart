import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'services/local_storage_service.dart';
import 'services/first_launch_service.dart';
import 'services/sample_data_service.dart';
import 'app.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  try {
    // Initialize local storage service
    await LocalStorageService.instance.init();

    // Initialize first launch service
    await FirstLaunchService.instance.init();

    // Add sample data if this is the first launch
    await SampleDataService.instance.addSampleDataIfFirstLaunch();

  } catch (e) {
    debugPrint('Error initializing services: $e');
  }

  // Set preferred orientations (portrait only)
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const GroceryGoApp());
}
