import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/grocery_provider.dart';
import '../models/grocery_item.dart';
import '../utils/constants.dart';

class FrequentItemsScreen extends StatelessWidget {
  const FrequentItemsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Frequent Items',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Consumer<GroceryProvider>(
        builder: (context, provider, child) {
          final frequentItems = provider.frequentItems;

          if (frequentItems.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.history,
                    size: 80,
                    color: AppConstants.secondaryTextColor,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    AppConstants.noFrequentItemsMessage,
                    textAlign: TextAlign.center,
                    style: AppConstants.subtitleTextStyle,
                  ),
                  const SizedBox(height: AppConstants.largePadding),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Items'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Header info
              Container(
                margin: const EdgeInsets.all(AppConstants.defaultPadding),
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppConstants.cardColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppConstants.primaryColor,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        'Items you\'ve added ${AppConstants.frequentItemThreshold} or more times. Tap to add to your current list.',
                        style: AppConstants.bodyTextStyle,
                      ),
                    ),
                  ],
                ),
              ),
              // Frequent items list
              Expanded(
                child: ListView.builder(
                  itemCount: frequentItems.length,
                  itemBuilder: (context, index) {
                    final item = frequentItems[index];
                    return FrequentItemTile(item: item);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class FrequentItemTile extends StatelessWidget {
  final GroceryItem item;

  const FrequentItemTile({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding / 2,
      ),
      elevation: 2,
      color: AppConstants.cardColor,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
          child: Text(
            item.frequency.toString(),
            style: TextStyle(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          item.name,
          style: AppConstants.bodyTextStyle,
        ),
        subtitle: Text(
          'Added ${item.frequency} times',
          style: TextStyle(
            fontSize: 12,
            color: AppConstants.secondaryTextColor,
          ),
        ),
        trailing: IconButton(
          icon: Icon(
            Icons.add_circle,
            color: AppConstants.primaryColor,
            size: 28,
          ),
          onPressed: () => _addToList(context),
          tooltip: 'Add to list',
        ),
        onTap: () => _addToList(context),
      ),
    );
  }

  Future<void> _addToList(BuildContext context) async {
    final provider = context.read<GroceryProvider>();
    final success = await provider.addFromFrequentItems(item);

    if (success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${item.name} added to your list'),
          duration: const Duration(seconds: 2),
          backgroundColor: AppConstants.primaryColor,
          action: SnackBarAction(
            label: 'View List',
            textColor: Colors.white,
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      );
    }
  }
}
