// This is a basic Flutter widget test for GroceryGo app.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:grocerygo/app.dart';

void main() {
  testWidgets('GroceryGo app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const GroceryGoApp());
    await tester.pumpAndSettle();

    // Verify that the app title is displayed
    expect(find.text('GroceryGo'), findsOneWidget);

    // Verify that the floating action button is present
    expect(find.byIcon(Icons.add), findsOneWidget);

    // Tap the '+' icon to open add item dialog
    await tester.tap(find.byIcon(Icons.add));
    await tester.pumpAndSettle();

    // Verify that the add item dialog is displayed
    expect(find.text('Add Grocery Item'), findsOneWidget);
  });
}
