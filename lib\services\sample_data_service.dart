import 'package:flutter/foundation.dart';
import '../models/grocery_item.dart';
import '../services/local_storage_service.dart';
import '../services/first_launch_service.dart';

class SampleDataService {
  static SampleDataService? _instance;
  static SampleDataService get instance => _instance ??= SampleDataService._();
  
  SampleDataService._();

  final LocalStorageService _storageService = LocalStorageService.instance;
  final FirstLaunchService _firstLaunchService = FirstLaunchService.instance;

  /// Sample grocery items with realistic data
  List<Map<String, dynamic>> get _sampleItems => [
    {
      'name': 'Milk',
      'isChecked': false,
      'frequency': 5,
      'daysAgo': 2,
    },
    {
      'name': 'Bread',
      'isChecked': true,
      'frequency': 4,
      'daysAgo': 1,
    },
    {
      'name': 'Bananas',
      'isChecked': false,
      'frequency': 3,
      'daysAgo': 3,
    },
    {
      'name': 'Chicken Breast',
      'isChecked': true,
      'frequency': 3,
      'daysAgo': 0,
    },
    {
      'name': 'Eggs',
      'isChecked': false,
      'frequency': 4,
      'daysAgo': 1,
    },
    {
      'name': 'Apples',
      'isChecked': true,
      'frequency': 2,
      'daysAgo': 2,
    },
    {
      'name': 'Rice',
      'isChecked': false,
      'frequency': 1,
      'daysAgo': 4,
    },
    {
      'name': 'Yogurt',
      'isChecked': false,
      'frequency': 3,
      'daysAgo': 1,
    },
  ];

  /// Add sample data to the app on first launch
  Future<bool> addSampleDataIfFirstLaunch() async {
    try {
      // Check if this is the first launch and sample data hasn't been added
      final isFirstLaunch = await _firstLaunchService.isFirstLaunch();
      final hasSampleData = await _firstLaunchService.hasSampleDataBeenAdded();
      
      debugPrint('SampleDataService: isFirstLaunch = $isFirstLaunch, hasSampleData = $hasSampleData');
      
      if (isFirstLaunch && !hasSampleData) {
        await _addSampleItems();
        await _firstLaunchService.markSampleDataAdded();
        await _firstLaunchService.markFirstLaunchComplete();
        debugPrint('SampleDataService: Sample data added successfully');
        return true;
      }
      
      // If not first launch, just mark first launch as complete
      if (isFirstLaunch) {
        await _firstLaunchService.markFirstLaunchComplete();
      }
      
      return false;
    } catch (e) {
      debugPrint('SampleDataService: Error adding sample data: $e');
      return false;
    }
  }

  /// Add the sample items to storage
  Future<void> _addSampleItems() async {
    for (final itemData in _sampleItems) {
      final item = GroceryItem(
        name: itemData['name'] as String,
        isChecked: itemData['isChecked'] as bool,
        frequency: itemData['frequency'] as int,
        createdAt: DateTime.now().subtract(
          Duration(days: itemData['daysAgo'] as int),
        ),
      );
      
      await _storageService.addItem(item);
      debugPrint('SampleDataService: Added sample item: ${item.name}');
    }
  }

  /// Check if the current data appears to be sample data
  Future<bool> isSampleDataPresent() async {
    try {
      final allItems = _storageService.getAllItems();
      
      if (allItems.isEmpty) {
        return false;
      }
      
      // Check if at least 50% of sample item names are present
      final sampleNames = _sampleItems.map((item) => item['name'] as String).toSet();
      final currentNames = allItems.map((item) => item.name).toSet();
      
      final matchingItems = sampleNames.intersection(currentNames);
      final matchPercentage = matchingItems.length / sampleNames.length;
      
      debugPrint('SampleDataService: Sample data match percentage: ${(matchPercentage * 100).toStringAsFixed(1)}%');
      
      return matchPercentage >= 0.5; // 50% or more matches
    } catch (e) {
      debugPrint('SampleDataService: Error checking sample data presence: $e');
      return false;
    }
  }

  /// Get a welcome message for first-time users
  String getWelcomeMessage() {
    return "Welcome to GroceryGo! We've added some sample items to help you get started. You can delete them anytime by long-pressing or using the menu options.";
  }

  /// Get sample data statistics
  Map<String, int> getSampleDataStats() {
    final totalItems = _sampleItems.length;
    final checkedItems = _sampleItems.where((item) => item['isChecked'] as bool).length;
    final frequentItems = _sampleItems.where((item) => (item['frequency'] as int) >= 2).length;
    
    return {
      'total': totalItems,
      'checked': checkedItems,
      'unchecked': totalItems - checkedItems,
      'frequent': frequentItems,
    };
  }

  /// Reset sample data (for testing purposes)
  Future<void> resetSampleData() async {
    try {
      await _storageService.clearAllItems();
      await _firstLaunchService.resetFirstLaunchStatus();
      debugPrint('SampleDataService: Sample data reset completed');
    } catch (e) {
      debugPrint('SampleDataService: Error resetting sample data: $e');
    }
  }

  /// Force add sample data (for testing)
  Future<void> forceAddSampleData() async {
    try {
      await _addSampleItems();
      await _firstLaunchService.markSampleDataAdded();
      debugPrint('SampleDataService: Sample data force added');
    } catch (e) {
      debugPrint('SampleDataService: Error force adding sample data: $e');
    }
  }
}
