import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/grocery_item.dart';
import '../models/grocery_category.dart';
import '../providers/grocery_provider.dart';
import '../widgets/add_item_dialog.dart';
import '../utils/constants.dart';

class AnimatedGroceryTile extends StatefulWidget {
  final GroceryItem item;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final int index;

  const AnimatedGroceryTile({
    super.key,
    required this.item,
    this.onTap,
    this.onLongPress,
    required this.index,
  });

  @override
  State<AnimatedGroceryTile> createState() => _AnimatedGroceryTileState();
}

class _AnimatedGroceryTileState extends State<AnimatedGroceryTile>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _checkController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _checkAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _slideController = AnimationController(
      duration: Duration(milliseconds: 300 + (widget.index * 50)),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _checkController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Initialize animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _checkAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _checkController,
      curve: Curves.elasticOut,
    ));

    // Start entrance animation
    _slideController.forward();
    
    // Set initial check state
    if (widget.item.isChecked) {
      _checkController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    _checkController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AnimatedGroceryTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Animate check state changes
    if (oldWidget.item.isChecked != widget.item.isChecked) {
      if (widget.item.isChecked) {
        _checkController.forward();
      } else {
        _checkController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Dismissible(
          key: Key(widget.item.key.toString()),
          direction: DismissDirection.endToStart,
          background: Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.only(right: 20),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.delete,
              color: Colors.white,
              size: 24,
            ),
          ),
          confirmDismiss: (direction) async {
            return await _showDeleteConfirmation(context);
          },
          onDismissed: (direction) {
            context.read<GroceryProvider>().removeItem(widget.item);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(AppConstants.itemRemovedMessage),
                duration: Duration(seconds: 2),
              ),
            );
          },
          child: GestureDetector(
            onTapDown: (_) {
              setState(() {
                _isPressed = true;
              });
              _scaleController.forward();
            },
            onTapUp: (_) {
              setState(() {
                _isPressed = false;
              });
              _scaleController.reverse();
            },
            onTapCancel: () {
              setState(() {
                _isPressed = false;
              });
              _scaleController.reverse();
            },
            child: AnimatedContainer(
              duration: AppConstants.shortAnimation,
              curve: Curves.easeInOut,
              margin: EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: AppConstants.smallPadding / 2,
              ),
              decoration: BoxDecoration(
                color: _isPressed 
                    ? AppConstants.primaryColor.withValues(alpha: 0.1)
                    : AppConstants.cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: _isPressed ? 0.3 : 0.1),
                    spreadRadius: _isPressed ? 2 : 1,
                    blurRadius: _isPressed ? 8 : 4,
                    offset: Offset(0, _isPressed ? 4 : 2),
                  ),
                ],
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                  vertical: AppConstants.smallPadding,
                ),
                leading: GestureDetector(
                  onTap: () {
                    context.read<GroceryProvider>().toggleItemCheck(widget.item);
                  },
                  child: AnimatedBuilder(
                    animation: _checkAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: 1.0 + (_checkAnimation.value * 0.2),
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: widget.item.isChecked 
                                  ? AppConstants.primaryColor 
                                  : Colors.grey.shade400,
                              width: 2,
                            ),
                            color: widget.item.isChecked 
                                ? AppConstants.primaryColor 
                                : Colors.transparent,
                          ),
                          child: widget.item.isChecked
                              ? Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : null,
                        ),
                      );
                    },
                  ),
                ),
                title: AnimatedDefaultTextStyle(
                  duration: AppConstants.shortAnimation,
                  style: widget.item.isChecked
                      ? AppConstants.checkedTextStyle
                      : AppConstants.bodyTextStyle,
                  child: Text(widget.item.quantityDisplay),
                ),
                subtitle: _buildSubtitle(),
                trailing: _buildTrailing(),
                onTap: widget.onTap ??
                    () {
                      context.read<GroceryProvider>().toggleItemCheck(widget.item);
                    },
                onLongPress: widget.onLongPress ??
                    () {
                      _showEditDialog(context);
                    },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubtitle() {
    return AnimatedOpacity(
      duration: AppConstants.shortAnimation,
      opacity: widget.item.isChecked ? 0.6 : 1.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category and frequency info
          Row(
            children: [
              // Category indicator
              AnimatedContainer(
                duration: AppConstants.shortAnimation,
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: GroceryCategories.getCategoryById(widget.item.category)
                      .color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      GroceryCategories.getCategoryById(widget.item.category).icon,
                      size: 12,
                      color: GroceryCategories.getCategoryById(widget.item.category).color,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      GroceryCategories.getCategoryById(widget.item.category).name,
                      style: TextStyle(
                        fontSize: 10,
                        color: GroceryCategories.getCategoryById(widget.item.category).color,
                        fontWeight: FontWeight.bold,
                        decoration: widget.item.isChecked ? TextDecoration.lineThrough : null,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.item.frequency > 1) ...[
                const SizedBox(width: 8),
                Text(
                  '${widget.item.frequency}x bought',
                  style: TextStyle(
                    fontSize: 10,
                    color: AppConstants.secondaryTextColor,
                    decoration: widget.item.isChecked ? TextDecoration.lineThrough : null,
                  ),
                ),
              ],
            ],
          ),
          // Price and notes
          if (widget.item.price != null || widget.item.notes != null) ...[
            const SizedBox(height: 2),
            Row(
              children: [
                if (widget.item.price != null) ...[
                  AnimatedDefaultTextStyle(
                    duration: AppConstants.shortAnimation,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                      decoration: widget.item.isChecked ? TextDecoration.lineThrough : null,
                    ),
                    child: Text(widget.item.totalPriceDisplay),
                  ),
                  if (widget.item.notes != null) const SizedBox(width: 8),
                ],
                if (widget.item.notes != null)
                  Expanded(
                    child: AnimatedDefaultTextStyle(
                      duration: AppConstants.shortAnimation,
                      style: TextStyle(
                        fontSize: 11,
                        color: AppConstants.secondaryTextColor,
                        fontStyle: FontStyle.italic,
                        decoration: widget.item.isChecked ? TextDecoration.lineThrough : null,
                      ),
                      child: Text(
                        widget.item.notes!,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTrailing() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.item.isChecked)
          AnimatedBuilder(
            animation: _checkAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _checkAnimation.value,
                child: Icon(
                  Icons.check_circle,
                  color: AppConstants.primaryColor,
                ),
              );
            },
          )
        else
          AnimatedOpacity(
            duration: AppConstants.shortAnimation,
            opacity: _isPressed ? 1.0 : 0.7,
            child: IconButton(
              icon: Icon(
                Icons.edit,
                color: AppConstants.secondaryTextColor,
                size: 20,
              ),
              onPressed: () {
                _showEditDialog(context);
              },
              tooltip: 'Edit item',
            ),
          ),
      ],
    );
  }

  Future<bool?> _showDeleteConfirmation(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(AppConstants.confirmDeleteTitle),
          content: Text(
            '${AppConstants.confirmDeleteMessage}\n\n"${widget.item.name}"',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AddItemDialog(editItem: widget.item);
      },
    );
  }
}
