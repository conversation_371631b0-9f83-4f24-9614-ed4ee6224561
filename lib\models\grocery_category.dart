import 'package:flutter/material.dart';

class GroceryCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final List<String> commonItems;

  const GroceryCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.commonItems,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroceryCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class GroceryCategories {
  static const List<GroceryCategory> predefinedCategories = [
    GroceryCategory(
      id: 'dairy',
      name: 'Dairy & Eggs',
      description: 'Milk, cheese, yogurt, eggs, and dairy products',
      icon: Icons.egg_outlined,
      color: Color(0xFF2196F3), // Blue
      commonItems: ['Milk', 'Eggs', 'Cheese', 'Yogurt', 'Butter', 'Cream'],
    ),
    GroceryCategory(
      id: 'produce',
      name: 'Produce',
      description: 'Fresh fruits and vegetables',
      icon: Icons.eco,
      color: Color(0xFF4CAF50), // Green
      commonItems: ['Bananas', 'Apples', 'Carrots', 'Lettuce', 'Tomatoes', 'Onions'],
    ),
    GroceryCategory(
      id: 'meat',
      name: 'Meat & Seafood',
      description: 'Fresh and frozen meat, poultry, and seafood',
      icon: Icons.set_meal,
      color: Color(0xFFE91E63), // Pink
      commonItems: ['Chicken Breast', 'Ground Beef', 'Salmon', 'Pork', 'Turkey', 'Shrimp'],
    ),
    GroceryCategory(
      id: 'pantry',
      name: 'Pantry',
      description: 'Dry goods, canned items, and shelf-stable products',
      icon: Icons.kitchen,
      color: Color(0xFFFF9800), // Orange
      commonItems: ['Rice', 'Pasta', 'Bread', 'Cereal', 'Canned Beans', 'Flour'],
    ),
    GroceryCategory(
      id: 'frozen',
      name: 'Frozen',
      description: 'Frozen foods and ice cream',
      icon: Icons.ac_unit,
      color: Color(0xFF00BCD4), // Cyan
      commonItems: ['Frozen Vegetables', 'Ice Cream', 'Frozen Pizza', 'Frozen Berries'],
    ),
    GroceryCategory(
      id: 'beverages',
      name: 'Beverages',
      description: 'Drinks, juices, and liquid refreshments',
      icon: Icons.local_drink,
      color: Color(0xFF9C27B0), // Purple
      commonItems: ['Water', 'Juice', 'Soda', 'Coffee', 'Tea', 'Energy Drinks'],
    ),
    GroceryCategory(
      id: 'snacks',
      name: 'Snacks',
      description: 'Chips, crackers, and snack foods',
      icon: Icons.cookie,
      color: Color(0xFFFF5722), // Deep Orange
      commonItems: ['Chips', 'Crackers', 'Nuts', 'Granola Bars', 'Popcorn'],
    ),
    GroceryCategory(
      id: 'bakery',
      name: 'Bakery',
      description: 'Fresh baked goods and bread',
      icon: Icons.bakery_dining,
      color: Color(0xFF795548), // Brown
      commonItems: ['Bread', 'Bagels', 'Muffins', 'Croissants', 'Donuts'],
    ),
    GroceryCategory(
      id: 'deli',
      name: 'Deli',
      description: 'Prepared foods and deli meats',
      icon: Icons.lunch_dining,
      color: Color(0xFF607D8B), // Blue Grey
      commonItems: ['Deli Meat', 'Prepared Salads', 'Rotisserie Chicken', 'Sandwiches'],
    ),
    GroceryCategory(
      id: 'household',
      name: 'Household',
      description: 'Cleaning supplies and household items',
      icon: Icons.cleaning_services,
      color: Color(0xFF9E9E9E), // Grey
      commonItems: ['Paper Towels', 'Toilet Paper', 'Detergent', 'Soap', 'Trash Bags'],
    ),
    GroceryCategory(
      id: 'personal_care',
      name: 'Personal Care',
      description: 'Health and beauty products',
      icon: Icons.face_retouching_natural,
      color: Color(0xFFE1BEE7), // Light Purple
      commonItems: ['Shampoo', 'Toothpaste', 'Deodorant', 'Lotion', 'Vitamins'],
    ),
    GroceryCategory(
      id: 'other',
      name: 'Other',
      description: 'Miscellaneous items',
      icon: Icons.more_horiz,
      color: Color(0xFF757575), // Dark Grey
      commonItems: [],
    ),
  ];

  static GroceryCategory getCategoryById(String id) {
    return predefinedCategories.firstWhere(
      (category) => category.id == id,
      orElse: () => predefinedCategories.last, // Return 'Other' as default
    );
  }

  static GroceryCategory getCategoryByName(String name) {
    return predefinedCategories.firstWhere(
      (category) => category.name.toLowerCase() == name.toLowerCase(),
      orElse: () => predefinedCategories.last, // Return 'Other' as default
    );
  }

  static List<String> getAllCategoryIds() {
    return predefinedCategories.map((category) => category.id).toList();
  }

  static List<String> getAllCategoryNames() {
    return predefinedCategories.map((category) => category.name).toList();
  }

  static GroceryCategory suggestCategoryForItem(String itemName) {
    final lowerItemName = itemName.toLowerCase();
    
    for (final category in predefinedCategories) {
      for (final commonItem in category.commonItems) {
        if (lowerItemName.contains(commonItem.toLowerCase()) ||
            commonItem.toLowerCase().contains(lowerItemName)) {
          return category;
        }
      }
    }
    
    return predefinedCategories.last; // Return 'Other' as default
  }

  static Map<String, List<String>> getCategoryItemSuggestions() {
    final Map<String, List<String>> suggestions = {};
    
    for (final category in predefinedCategories) {
      suggestions[category.id] = category.commonItems;
    }
    
    return suggestions;
  }
}
