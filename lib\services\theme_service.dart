import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService {
  static ThemeService? _instance;
  static ThemeService get instance => _instance ??= ThemeService._();
  
  ThemeService._();

  static const String _themeKey = 'app_theme_mode';
  static const String _primaryColorKey = 'primary_color';
  
  SharedPreferences? _prefs;

  /// Initialize the service
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Get the current theme mode
  Future<ThemeMode> getThemeMode() async {
    if (_prefs == null) await init();
    
    final themeIndex = _prefs!.getInt(_themeKey) ?? 0;
    return ThemeMode.values[themeIndex];
  }

  /// Set the theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_prefs == null) await init();
    
    await _prefs!.setInt(_themeKey, mode.index);
  }

  /// Get the primary color
  Future<Color> getPrimaryColor() async {
    if (_prefs == null) await init();

    final colorValue = _prefs!.getInt(_primaryColorKey) ?? Colors.green.value;
    return Color(colorValue);
  }

  /// Set the primary color
  Future<void> setPrimaryColor(Color color) async {
    if (_prefs == null) await init();

    await _prefs!.setInt(_primaryColorKey, color.value);
  }

  /// Get available theme colors
  static List<Color> get availableColors => [
    Colors.green,
    Colors.blue,
    Colors.purple,
    Colors.orange,
    Colors.red,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
  ];

  /// Get light theme
  static ThemeData getLightTheme(Color primaryColor) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: MaterialColor(primaryColor.value, _generateSwatch(primaryColor)),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: Colors.grey.shade50,
      cardColor: Colors.white,
      
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
      ),
      
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedItemColor: primaryColor,
        unselectedItemColor: Colors.grey.shade600,
        backgroundColor: Colors.white,
        elevation: 8,
      ),
      
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      ),
    );
  }

  /// Get dark theme
  static ThemeData getDarkTheme(Color primaryColor) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: MaterialColor(primaryColor.value, _generateSwatch(primaryColor)),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: Colors.grey.shade900,
      cardColor: Colors.grey.shade800,
      
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
      ),
      
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedItemColor: primaryColor,
        unselectedItemColor: Colors.grey.shade400,
        backgroundColor: Colors.grey.shade800,
        elevation: 8,
      ),
      
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
      ),
    );
  }

  /// Generate material color swatch
  static Map<int, Color> _generateSwatch(Color color) {
    final hsl = HSLColor.fromColor(color);
    return {
      50: hsl.withLightness(0.95).toColor(),
      100: hsl.withLightness(0.9).toColor(),
      200: hsl.withLightness(0.8).toColor(),
      300: hsl.withLightness(0.7).toColor(),
      400: hsl.withLightness(0.6).toColor(),
      500: color,
      600: hsl.withLightness(0.4).toColor(),
      700: hsl.withLightness(0.3).toColor(),
      800: hsl.withLightness(0.2).toColor(),
      900: hsl.withLightness(0.1).toColor(),
    };
  }
}
