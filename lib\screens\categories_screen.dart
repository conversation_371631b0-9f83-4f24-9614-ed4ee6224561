import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/grocery_provider.dart';
import '../models/grocery_category.dart';
import '../widgets/grocery_tile.dart';
import '../utils/constants.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  String? _selectedCategoryId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Categories',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          // Category overview card
          Container(
            margin: const EdgeInsets.all(AppConstants.defaultPadding),
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppConstants.cardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final categoryStats = _getCategoryStatistics(provider.items);
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.analytics, color: AppConstants.primaryColor),
                        const SizedBox(width: 8),
                        const Text(
                          'Category Overview',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '${categoryStats.length} categories with items • ${provider.itemsCount} total items',
                      style: TextStyle(
                        color: AppConstants.secondaryTextColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),

          // Category filter chips
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: GroceryCategories.predefinedCategories.length,
              itemBuilder: (context, index) {
                final category = GroceryCategories.predefinedCategories[index];
                final isSelected = _selectedCategoryId == category.id;

                return Consumer<GroceryProvider>(
                  builder: (context, provider, child) {
                    final itemCount = provider.items.where((item) => item.category == category.id).length;

                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              category.icon,
                              size: 16,
                              color: isSelected ? Colors.white : category.color,
                            ),
                            const SizedBox(width: 4),
                            Text(category.name),
                            if (itemCount > 0) ...[
                              const SizedBox(width: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: BoxDecoration(
                                  color: isSelected ? Colors.white.withValues(alpha: 0.3) : category.color.withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '$itemCount',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: isSelected ? Colors.white : category.color,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategoryId = selected ? category.id : null;
                          });
                        },
                        backgroundColor: category.color.withValues(alpha: 0.1),
                        selectedColor: category.color,
                        labelStyle: TextStyle(
                          color: isSelected ? Colors.white : AppConstants.textColor,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          // Items list
          Expanded(
            child: Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final filteredItems = _selectedCategoryId == null
                    ? provider.items
                    : provider.items.where((item) => item.category == _selectedCategoryId).toList();

                if (filteredItems.isEmpty) {
                  final categoryName = _selectedCategoryId == null 
                      ? 'any category'
                      : GroceryCategories.getCategoryById(_selectedCategoryId!).name;
                  
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.category_outlined,
                          size: 80,
                          color: AppConstants.secondaryTextColor,
                        ),
                        const SizedBox(height: AppConstants.defaultPadding),
                        Text(
                          'No items in $categoryName',
                          textAlign: TextAlign.center,
                          style: AppConstants.subtitleTextStyle,
                        ),
                      ],
                    ),
                  );
                }

                // Group items by category for display
                final groupedItems = <String, List<dynamic>>{};
                for (final item in filteredItems) {
                  final categoryId = item.category;
                  if (!groupedItems.containsKey(categoryId)) {
                    groupedItems[categoryId] = [];
                  }
                  groupedItems[categoryId]!.add(item);
                }

                return ListView.builder(
                  itemCount: groupedItems.length,
                  itemBuilder: (context, index) {
                    final categoryId = groupedItems.keys.elementAt(index);
                    final categoryItems = groupedItems[categoryId]!;
                    final category = GroceryCategories.getCategoryById(categoryId);

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Category header
                        if (_selectedCategoryId == null)
                          Container(
                            padding: const EdgeInsets.all(AppConstants.defaultPadding),
                            child: Row(
                              children: [
                                Icon(
                                  category.icon,
                                  color: category.color,
                                  size: 24,
                                ),
                                const SizedBox(width: AppConstants.smallPadding),
                                Text(
                                  category.name,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: category.color,
                                  ),
                                ),
                                const SizedBox(width: AppConstants.smallPadding),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: category.color.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${categoryItems.length}',
                                    style: TextStyle(
                                      color: category.color,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        // Category items
                        ...categoryItems.map((item) => GroceryTile(item: item)),
                        if (_selectedCategoryId == null)
                          const SizedBox(height: AppConstants.smallPadding),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Map<String, int> _getCategoryStatistics(List<dynamic> items) {
    final Map<String, int> stats = {};

    for (final item in items) {
      final categoryId = item.category as String;
      stats[categoryId] = (stats[categoryId] ?? 0) + 1;
    }

    return stats;
  }

  Widget _buildCategoryStatsCard(BuildContext context) {
    return Consumer<GroceryProvider>(
      builder: (context, provider, child) {
        final categoryStats = _getCategoryStatistics(provider.items);
        final sortedCategories = GroceryCategories.predefinedCategories
            .where((cat) => categoryStats.containsKey(cat.id))
            .toList()
          ..sort((a, b) => (categoryStats[b.id] ?? 0).compareTo(categoryStats[a.id] ?? 0));

        return Card(
          margin: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.pie_chart, color: AppConstants.primaryColor),
                    const SizedBox(width: 8),
                    const Text(
                      'Category Breakdown',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ...sortedCategories.take(5).map((category) {
                  final count = categoryStats[category.id] ?? 0;
                  final percentage = provider.itemsCount > 0
                      ? (count / provider.itemsCount * 100).round()
                      : 0;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Icon(
                          category.icon,
                          size: 16,
                          color: category.color,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            category.name,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                        Text(
                          '$count items ($percentage%)',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppConstants.secondaryTextColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                if (sortedCategories.length > 5) ...[
                  const SizedBox(height: 8),
                  Text(
                    '+ ${sortedCategories.length - 5} more categories',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppConstants.secondaryTextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
