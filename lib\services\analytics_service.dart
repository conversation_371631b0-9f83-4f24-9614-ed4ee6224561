import 'package:flutter/foundation.dart';
import '../models/grocery_item.dart';
import '../models/grocery_category.dart';
import '../services/local_storage_service.dart';

class AnalyticsService {
  static AnalyticsService? _instance;
  static AnalyticsService get instance => _instance ??= AnalyticsService._();
  
  AnalyticsService._();

  final LocalStorageService _storageService = LocalStorageService.instance;

  /// Get shopping analytics for the past period
  Map<String, dynamic> getShoppingAnalytics({int days = 30}) {
    try {
      final allItems = _storageService.getAllItems();
      final cutoffDate = DateTime.now().subtract(Duration(days: days));
      
      // Filter items within the time period
      final recentItems = allItems.where((item) => 
          item.createdAt.isAfter(cutoffDate)).toList();
      
      // Calculate statistics
      final totalItems = allItems.length;
      final recentItemsCount = recentItems.length;
      final checkedItems = allItems.where((item) => item.isChecked).length;
      final completionRate = totalItems > 0 ? (checkedItems / totalItems * 100).round() : 0;
      
      // Category breakdown
      final categoryStats = <String, int>{};
      for (final item in allItems) {
        categoryStats[item.category] = (categoryStats[item.category] ?? 0) + 1;
      }
      
      // Most frequent items
      final frequentItems = allItems
          .where((item) => item.frequency >= 2)
          .toList()
        ..sort((a, b) => b.frequency.compareTo(a.frequency));
      
      // Price analytics
      final itemsWithPrice = allItems.where((item) => item.price != null).toList();
      final totalSpent = itemsWithPrice.fold<double>(
          0, (sum, item) => sum + (item.totalPrice ?? 0));
      final averageItemPrice = itemsWithPrice.isNotEmpty
          ? totalSpent / itemsWithPrice.length
          : 0.0;
      
      // Shopping patterns
      final shoppingDays = _getShoppingDayPatterns(allItems);
      final popularCategories = _getPopularCategories(categoryStats);
      
      return {
        'totalItems': totalItems,
        'recentItems': recentItemsCount,
        'checkedItems': checkedItems,
        'completionRate': completionRate,
        'categoryStats': categoryStats,
        'frequentItems': frequentItems.take(10).toList(),
        'totalSpent': totalSpent,
        'averageItemPrice': averageItemPrice,
        'itemsWithPrice': itemsWithPrice.length,
        'shoppingDays': shoppingDays,
        'popularCategories': popularCategories,
        'period': days,
      };
    } catch (e) {
      debugPrint('AnalyticsService: Error getting analytics: $e');
      return {};
    }
  }

  /// Get smart suggestions based on shopping patterns
  List<String> getSmartSuggestions({int maxSuggestions = 5}) {
    try {
      final allItems = _storageService.getAllItems();
      final suggestions = <String>[];
      
      // Suggest frequently bought items that aren't currently in the list
      final frequentItems = allItems
          .where((item) => item.frequency >= 3 && !item.isChecked)
          .toList()
        ..sort((a, b) => b.frequency.compareTo(a.frequency));
      
      for (final item in frequentItems.take(maxSuggestions)) {
        suggestions.add('${item.name} (bought ${item.frequency} times)');
      }
      
      // Add seasonal suggestions
      final seasonalItems = _getSeasonalSuggestions();
      suggestions.addAll(seasonalItems.take(maxSuggestions - suggestions.length));
      
      return suggestions;
    } catch (e) {
      debugPrint('AnalyticsService: Error getting suggestions: $e');
      return [];
    }
  }

  /// Get recipe-based suggestions
  List<String> getRecipeSuggestions() {
    final recipes = [
      'Pasta Night: Pasta, Tomato Sauce, Cheese, Garlic',
      'Breakfast Essentials: Eggs, Milk, Bread, Butter',
      'Salad Bowl: Lettuce, Tomatoes, Cucumber, Dressing',
      'Stir Fry: Rice, Vegetables, Soy Sauce, Oil',
      'Sandwich Lunch: Bread, Deli Meat, Cheese, Mayo',
    ];
    
    return recipes;
  }

  /// Get spending insights
  Map<String, dynamic> getSpendingInsights() {
    try {
      final allItems = _storageService.getAllItems();
      final itemsWithPrice = allItems.where((item) => item.price != null).toList();
      
      if (itemsWithPrice.isEmpty) {
        return {
          'hasData': false,
          'message': 'Add prices to items to see spending insights',
        };
      }
      
      // Category spending
      final categorySpending = <String, double>{};
      for (final item in itemsWithPrice) {
        final category = item.category;
        categorySpending[category] = (categorySpending[category] ?? 0) + (item.totalPrice ?? 0);
      }
      
      // Most expensive categories
      final sortedCategories = categorySpending.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      // Budget recommendations
      final totalSpent = categorySpending.values.fold<double>(0, (sum, amount) => sum + amount);
      final averagePerCategory = totalSpent / categorySpending.length;
      
      return {
        'hasData': true,
        'totalSpent': totalSpent,
        'categorySpending': categorySpending,
        'topSpendingCategories': sortedCategories.take(5).toList(),
        'averagePerCategory': averagePerCategory,
        'itemsTracked': itemsWithPrice.length,
      };
    } catch (e) {
      debugPrint('AnalyticsService: Error getting spending insights: $e');
      return {'hasData': false, 'message': 'Error calculating spending insights'};
    }
  }

  /// Get shopping day patterns
  Map<String, int> _getShoppingDayPatterns(List<GroceryItem> items) {
    final dayPatterns = <String, int>{};
    final dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    
    for (final item in items) {
      final dayOfWeek = item.createdAt.weekday - 1; // 0-6
      final dayName = dayNames[dayOfWeek];
      dayPatterns[dayName] = (dayPatterns[dayName] ?? 0) + 1;
    }
    
    return dayPatterns;
  }

  /// Get popular categories
  List<MapEntry<String, int>> _getPopularCategories(Map<String, int> categoryStats) {
    final sorted = categoryStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(5).toList();
  }

  /// Get seasonal suggestions based on current month
  List<String> _getSeasonalSuggestions() {
    final month = DateTime.now().month;
    
    switch (month) {
      case 12:
      case 1:
      case 2: // Winter
        return ['Hot Chocolate', 'Soup Ingredients', 'Citrus Fruits', 'Root Vegetables'];
      case 3:
      case 4:
      case 5: // Spring
        return ['Fresh Herbs', 'Spring Vegetables', 'Berries', 'Light Salads'];
      case 6:
      case 7:
      case 8: // Summer
        return ['BBQ Supplies', 'Fresh Fruits', 'Ice Cream', 'Cold Drinks'];
      case 9:
      case 10:
      case 11: // Fall
        return ['Pumpkin', 'Apples', 'Warm Spices', 'Comfort Foods'];
      default:
        return ['Fresh Produce', 'Seasonal Items'];
    }
  }

  /// Get shopping efficiency score
  int getShoppingEfficiencyScore() {
    try {
      final analytics = getShoppingAnalytics();
      final completionRate = analytics['completionRate'] as int? ?? 0;
      final frequentItemsCount = (analytics['frequentItems'] as List?)?.length ?? 0;
      final categoryDiversity = (analytics['categoryStats'] as Map?)?.length ?? 0;
      
      // Calculate score based on multiple factors
      int score = 0;
      
      // Completion rate (0-40 points)
      score += (completionRate * 0.4).round();
      
      // Frequent items usage (0-30 points)
      score += (frequentItemsCount * 3).clamp(0, 30);
      
      // Category diversity (0-30 points)
      score += (categoryDiversity * 5).clamp(0, 30);
      
      return score.clamp(0, 100);
    } catch (e) {
      debugPrint('AnalyticsService: Error calculating efficiency score: $e');
      return 0;
    }
  }
}
