import 'package:flutter/foundation.dart';
import '../models/grocery_item.dart';
import '../services/local_storage_service.dart';
import '../utils/constants.dart';

class GroceryProvider extends ChangeNotifier {
  final LocalStorageService _storageService = LocalStorageService.instance;
  
  List<GroceryItem> _items = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<GroceryItem> get items => List.unmodifiable(_items);
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Get unchecked items (current shopping list)
  List<GroceryItem> get uncheckedItems => 
      _items.where((item) => !item.isChecked).toList();
  
  // Get checked items
  List<GroceryItem> get checkedItems => 
      _items.where((item) => item.isChecked).toList();
  
  // Get frequently bought items
  List<GroceryItem> get frequentItems => 
      _storageService.getFrequentItems();
  
  // Get items count
  int get itemsCount => _items.length;
  int get uncheckedItemsCount => uncheckedItems.length;
  int get checkedItemsCount => checkedItems.length;

  /// Initialize the provider and load items
  Future<void> init() async {
    await loadItems();
  }

  /// Load all items from storage
  Future<void> loadItems() async {
    _setLoading(true);
    _clearError();
    
    try {
      _items = _storageService.getAllItems();
      _sortItems();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load items: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new item to the list
  Future<bool> addItem(String name) async {
    if (name.trim().isEmpty) {
      _setError(AppConstants.emptyItemNameError);
      return false;
    }

    if (name.length > AppConstants.maxItemNameLength) {
      _setError(AppConstants.itemNameTooLongError);
      return false;
    }

    _clearError();

    try {
      final trimmedName = name.trim();
      
      // Check if item already exists
      final existingItem = _storageService.findItemByName(trimmedName);
      
      if (existingItem != null) {
        // Increment frequency and uncheck if it was checked
        existingItem.frequency++;
        existingItem.isChecked = false;
        await _storageService.updateItem(existingItem);
      } else {
        // Create new item
        final newItem = GroceryItem(name: trimmedName);
        await _storageService.addItem(newItem);
      }
      
      await loadItems();
      return true;
    } catch (e) {
      _setError('Failed to add item: $e');
      return false;
    }
  }

  /// Toggle the checked status of an item
  Future<void> toggleItemCheck(GroceryItem item) async {
    try {
      item.isChecked = !item.isChecked;
      await _storageService.updateItem(item);
      _sortItems();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update item: $e');
    }
  }

  /// Remove an item from the list
  Future<void> removeItem(GroceryItem item) async {
    try {
      await _storageService.deleteItem(item);
      _items.remove(item);
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove item: $e');
    }
  }

  /// Add item from frequent items list
  Future<bool> addFromFrequentItems(GroceryItem frequentItem) async {
    return await addItem(frequentItem.name);
  }

  /// Clear all checked items
  Future<void> clearCheckedItems() async {
    try {
      final checkedItems = _items.where((item) => item.isChecked).toList();
      
      for (final item in checkedItems) {
        await _storageService.deleteItem(item);
        _items.remove(item);
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear checked items: $e');
    }
  }

  /// Clear all items
  Future<void> clearAllItems() async {
    try {
      await _storageService.clearAllItems();
      _items.clear();
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear all items: $e');
    }
  }

  /// Sort items: unchecked first, then checked, alphabetically within each group
  void _sortItems() {
    _items.sort((a, b) {
      // First sort by checked status (unchecked first)
      if (a.isChecked != b.isChecked) {
        return a.isChecked ? 1 : -1;
      }
      // Then sort alphabetically
      return a.name.toLowerCase().compareTo(b.name.toLowerCase());
    });
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Refresh the provider data
  Future<void> refresh() async {
    await loadItems();
  }
}
