import 'package:flutter/foundation.dart';
import '../models/grocery_item.dart';
import '../models/grocery_category.dart';
import '../services/local_storage_service.dart';

class SmartSuggestionsService {
  static SmartSuggestionsService? _instance;
  static SmartSuggestionsService get instance => _instance ??= SmartSuggestionsService._();
  
  SmartSuggestionsService._();

  final LocalStorageService _storageService = LocalStorageService.instance;

  /// Get smart suggestions based on various factors
  List<Map<String, dynamic>> getSmartSuggestions({int maxSuggestions = 10}) {
    try {
      final suggestions = <Map<String, dynamic>>[];
      
      // Add different types of suggestions
      suggestions.addAll(_getFrequencyBasedSuggestions());
      suggestions.addAll(_getSeasonalSuggestions());
      suggestions.addAll(_getComplementaryItemSuggestions());
      suggestions.addAll(_getCategoryBasedSuggestions());
      
      // Sort by relevance score and return top suggestions
      suggestions.sort((a, b) => (b['score'] as double).compareTo(a['score'] as double));
      
      return suggestions.take(maxSuggestions).toList();
    } catch (e) {
      debugPrint('SmartSuggestionsService: Error getting suggestions: $e');
      return [];
    }
  }

  /// Get suggestions based on item frequency
  List<Map<String, dynamic>> _getFrequencyBasedSuggestions() {
    final suggestions = <Map<String, dynamic>>[];
    final allItems = _storageService.getAllItems();
    
    // Find frequently bought items not currently in unchecked list
    final uncheckedItems = allItems.where((item) => !item.isChecked).map((item) => item.name.toLowerCase()).toSet();
    final frequentItems = allItems
        .where((item) => item.frequency >= 3)
        .where((item) => !uncheckedItems.contains(item.name.toLowerCase()))
        .toList()
      ..sort((a, b) => b.frequency.compareTo(a.frequency));
    
    for (final item in frequentItems.take(5)) {
      suggestions.add({
        'name': item.name,
        'type': 'frequent',
        'reason': 'You buy this ${item.frequency} times on average',
        'score': item.frequency * 2.0,
        'category': item.category,
        'icon': 'history',
        'item': item,
      });
    }
    
    return suggestions;
  }

  /// Get seasonal suggestions
  List<Map<String, dynamic>> _getSeasonalSuggestions() {
    final suggestions = <Map<String, dynamic>>[];
    final seasonalItems = _getCurrentSeasonalItems();
    
    for (final item in seasonalItems) {
      suggestions.add({
        'name': item['name'],
        'type': 'seasonal',
        'reason': item['reason'],
        'score': 1.5,
        'category': item['category'],
        'icon': 'eco',
      });
    }
    
    return suggestions;
  }

  /// Get complementary item suggestions
  List<Map<String, dynamic>> _getComplementaryItemSuggestions() {
    final suggestions = <Map<String, dynamic>>[];
    final allItems = _storageService.getAllItems();
    final uncheckedItems = allItems.where((item) => !item.isChecked).toList();
    
    // Define complementary item pairs
    final complementaryPairs = {
      'milk': ['cereal', 'cookies', 'coffee'],
      'bread': ['butter', 'jam', 'peanut butter'],
      'pasta': ['tomato sauce', 'cheese', 'garlic'],
      'chicken': ['rice', 'vegetables', 'seasoning'],
      'eggs': ['bacon', 'toast', 'cheese'],
      'bananas': ['peanut butter', 'oats', 'yogurt'],
      'lettuce': ['tomatoes', 'cucumber', 'dressing'],
    };
    
    for (final item in uncheckedItems) {
      final itemName = item.name.toLowerCase();
      for (final key in complementaryPairs.keys) {
        if (itemName.contains(key)) {
          final complements = complementaryPairs[key]!;
          for (final complement in complements) {
            // Check if complement is not already in the list
            final hasComplement = uncheckedItems.any((i) => 
                i.name.toLowerCase().contains(complement));
            
            if (!hasComplement) {
              suggestions.add({
                'name': _capitalizeFirst(complement),
                'type': 'complementary',
                'reason': 'Goes well with ${item.name}',
                'score': 1.2,
                'category': _suggestCategoryForItem(complement),
                'icon': 'lightbulb',
              });
            }
          }
          break;
        }
      }
    }
    
    return suggestions;
  }

  /// Get category-based suggestions
  List<Map<String, dynamic>> _getCategoryBasedSuggestions() {
    final suggestions = <Map<String, dynamic>>[];
    final allItems = _storageService.getAllItems();
    
    // Find categories with low item counts
    final categoryStats = <String, int>{};
    for (final item in allItems.where((item) => !item.isChecked)) {
      categoryStats[item.category] = (categoryStats[item.category] ?? 0) + 1;
    }
    
    // Suggest items for categories with 0-1 items
    for (final category in GroceryCategories.predefinedCategories) {
      final itemCount = categoryStats[category.id] ?? 0;
      if (itemCount <= 1 && category.commonItems.isNotEmpty) {
        final randomItem = category.commonItems.first;
        suggestions.add({
          'name': randomItem,
          'type': 'category',
          'reason': 'Popular ${category.name.toLowerCase()} item',
          'score': 1.0,
          'category': category.id,
          'icon': 'category',
        });
      }
    }
    
    return suggestions;
  }

  /// Get current seasonal items
  List<Map<String, dynamic>> _getCurrentSeasonalItems() {
    final month = DateTime.now().month;
    final season = _getCurrentSeason(month);
    
    switch (season) {
      case 'winter':
        return [
          {'name': 'Hot Chocolate', 'reason': 'Perfect for cold weather', 'category': 'beverages'},
          {'name': 'Soup Mix', 'reason': 'Warming winter comfort', 'category': 'pantry'},
          {'name': 'Oranges', 'reason': 'Winter citrus season', 'category': 'produce'},
        ];
      case 'spring':
        return [
          {'name': 'Fresh Herbs', 'reason': 'Spring herbs are fresh', 'category': 'produce'},
          {'name': 'Asparagus', 'reason': 'Spring vegetable season', 'category': 'produce'},
          {'name': 'Strawberries', 'reason': 'Spring berry season', 'category': 'produce'},
        ];
      case 'summer':
        return [
          {'name': 'Watermelon', 'reason': 'Refreshing summer fruit', 'category': 'produce'},
          {'name': 'Ice Cream', 'reason': 'Cool summer treat', 'category': 'frozen'},
          {'name': 'BBQ Sauce', 'reason': 'Summer grilling season', 'category': 'pantry'},
        ];
      case 'fall':
        return [
          {'name': 'Pumpkin', 'reason': 'Fall harvest season', 'category': 'produce'},
          {'name': 'Apples', 'reason': 'Apple picking season', 'category': 'produce'},
          {'name': 'Cinnamon', 'reason': 'Warm fall spices', 'category': 'pantry'},
        ];
      default:
        return [];
    }
  }

  /// Get recipe suggestions
  List<Map<String, dynamic>> getRecipeSuggestions() {
    return [
      {
        'name': 'Spaghetti Night',
        'items': ['Pasta', 'Tomato Sauce', 'Ground Beef', 'Parmesan Cheese'],
        'description': 'Classic comfort food dinner',
        'category': 'dinner',
      },
      {
        'name': 'Healthy Breakfast',
        'items': ['Oats', 'Bananas', 'Yogurt', 'Honey'],
        'description': 'Nutritious start to your day',
        'category': 'breakfast',
      },
      {
        'name': 'Fresh Salad',
        'items': ['Mixed Greens', 'Tomatoes', 'Cucumber', 'Olive Oil'],
        'description': 'Light and refreshing meal',
        'category': 'lunch',
      },
      {
        'name': 'Stir Fry',
        'items': ['Rice', 'Mixed Vegetables', 'Soy Sauce', 'Garlic'],
        'description': 'Quick and healthy dinner',
        'category': 'dinner',
      },
    ];
  }

  /// Get shopping reminders
  List<String> getShoppingReminders() {
    final allItems = _storageService.getAllItems();
    final reminders = <String>[];
    
    // Check for items that haven't been bought in a while
    final now = DateTime.now();
    for (final item in allItems) {
      if (item.frequency >= 3) {
        final daysSinceCreated = now.difference(item.createdAt).inDays;
        if (daysSinceCreated > 7) {
          reminders.add('You usually buy ${item.name} regularly');
        }
      }
    }
    
    // Add general reminders
    if (allItems.where((item) => !item.isChecked).length < 3) {
      reminders.add('Your shopping list is looking light');
    }
    
    return reminders.take(3).toList();
  }

  String _getCurrentSeason(int month) {
    if (month >= 12 || month <= 2) return 'winter';
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    return 'fall';
  }

  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  String _suggestCategoryForItem(String itemName) {
    return GroceryCategories.suggestCategoryForItem(itemName).id;
  }
}
