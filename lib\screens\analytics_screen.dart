import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/grocery_provider.dart';
import '../services/analytics_service.dart';
import '../services/smart_suggestions_service.dart';
import '../models/grocery_category.dart';
import '../utils/constants.dart';

class AnalyticsScreen extends StatelessWidget {
  const AnalyticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Shopping Insights',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOverviewCard(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildSmartSuggestionsCard(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildCategoryBreakdownCard(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildSpendingInsightsCard(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildShoppingPatternsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: AppConstants.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Shopping Overview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final analytics = AnalyticsService.instance.getShoppingAnalytics();
                final efficiencyScore = AnalyticsService.instance.getShoppingEfficiencyScore();
                
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatItem(
                            'Total Items',
                            '${analytics['totalItems'] ?? 0}',
                            Icons.list,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Completion Rate',
                            '${analytics['completionRate'] ?? 0}%',
                            Icons.check_circle,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatItem(
                            'Frequent Items',
                            '${(analytics['frequentItems'] as List?)?.length ?? 0}',
                            Icons.star,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Efficiency Score',
                            '$efficiencyScore/100',
                            Icons.trending_up,
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSmartSuggestionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: AppConstants.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Smart Suggestions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final suggestions = SmartSuggestionsService.instance.getSmartSuggestions(maxSuggestions: 5);
                
                if (suggestions.isEmpty) {
                  return const Text(
                    'Add more items to get personalized suggestions!',
                    style: TextStyle(
                      color: AppConstants.secondaryTextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  );
                }
                
                return Column(
                  children: suggestions.map((suggestion) {
                    final category = GroceryCategories.getCategoryById(suggestion['category'] ?? 'other');
                    
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            category.icon,
                            size: 16,
                            color: category.color,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  suggestion['name'],
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  suggestion['reason'],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppConstants.secondaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.add_circle_outline),
                            onPressed: () {
                              provider.addItem(suggestion['name']);
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('${suggestion['name']} added to your list'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            tooltip: 'Add to list',
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdownCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: AppConstants.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Category Breakdown',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final analytics = AnalyticsService.instance.getShoppingAnalytics();
                final categoryStats = analytics['categoryStats'] as Map<String, int>? ?? {};
                
                if (categoryStats.isEmpty) {
                  return const Text(
                    'No category data available yet.',
                    style: TextStyle(
                      color: AppConstants.secondaryTextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  );
                }
                
                final sortedCategories = categoryStats.entries.toList()
                  ..sort((a, b) => b.value.compareTo(a.value));
                
                return Column(
                  children: sortedCategories.take(5).map((entry) {
                    final category = GroceryCategories.getCategoryById(entry.key);
                    final percentage = provider.itemsCount > 0 
                        ? (entry.value / provider.itemsCount * 100).round()
                        : 0;
                    
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            category.icon,
                            size: 20,
                            color: category.color,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(category.name),
                          ),
                          Text(
                            '${entry.value} items ($percentage%)',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppConstants.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpendingInsightsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: AppConstants.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Spending Insights',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final insights = AnalyticsService.instance.getSpendingInsights();
                
                if (!(insights['hasData'] as bool? ?? false)) {
                  return Text(
                    insights['message'] ?? 'No spending data available',
                    style: const TextStyle(
                      color: AppConstants.secondaryTextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  );
                }
                
                final totalSpent = insights['totalSpent'] as double? ?? 0;
                final itemsTracked = insights['itemsTracked'] as int? ?? 0;
                final averagePerCategory = insights['averagePerCategory'] as double? ?? 0;
                
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatItem(
                            'Total Spent',
                            '\$${totalSpent.toStringAsFixed(2)}',
                            Icons.shopping_cart,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Items Tracked',
                            '$itemsTracked',
                            Icons.receipt,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildStatItem(
                      'Average per Category',
                      '\$${averagePerCategory.toStringAsFixed(2)}',
                      Icons.category,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShoppingPatternsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pattern, color: AppConstants.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Shopping Patterns',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<GroceryProvider>(
              builder: (context, provider, child) {
                final reminders = SmartSuggestionsService.instance.getShoppingReminders();
                
                if (reminders.isEmpty) {
                  return const Text(
                    'Keep shopping to see your patterns!',
                    style: TextStyle(
                      color: AppConstants.secondaryTextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  );
                }
                
                return Column(
                  children: reminders.map((reminder) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: AppConstants.primaryColor,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(reminder),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppConstants.primaryColor,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppConstants.textColor,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppConstants.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
