import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/grocery_item.dart';
import '../utils/constants.dart';

class LocalStorageService {
  static LocalStorageService? _instance;
  static LocalStorageService get instance => _instance ??= LocalStorageService._();
  
  LocalStorageService._();

  Box<GroceryItem>? _groceryBox;

  /// Initialize Hive and open the grocery items box
  Future<void> init() async {
    await Hive.initFlutter();
    
    // Register the GroceryItem adapter
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(GroceryItemAdapter());
    }
    
    // Open the grocery items box
    _groceryBox = await Hive.openBox<GroceryItem>(AppConstants.groceryBoxName);
  }

  /// Get the grocery box
  Box<GroceryItem> get groceryBox {
    if (_groceryBox == null || !_groceryBox!.isOpen) {
      throw Exception('Grocery box is not initialized. Call init() first.');
    }
    return _groceryBox!;
  }

  /// Get all grocery items
  List<GroceryItem> getAllItems() {
    try {
      return groceryBox.values.toList();
    } catch (e) {
      debugPrint('Error getting all items: $e');
      return [];
    }
  }

  /// Add a new grocery item
  Future<void> addItem(GroceryItem item) async {
    try {
      await groceryBox.add(item);
    } catch (e) {
      debugPrint('Error adding item: $e');
      rethrow;
    }
  }

  /// Update an existing grocery item
  Future<void> updateItem(GroceryItem item) async {
    try {
      await item.save();
    } catch (e) {
      debugPrint('Error updating item: $e');
      rethrow;
    }
  }

  /// Delete a grocery item
  Future<void> deleteItem(GroceryItem item) async {
    try {
      await item.delete();
    } catch (e) {
      debugPrint('Error deleting item: $e');
      rethrow;
    }
  }

  /// Find an item by name (case-insensitive)
  GroceryItem? findItemByName(String name) {
    try {
      return groceryBox.values.firstWhere(
        (item) => item.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get frequently bought items (frequency >= threshold)
  List<GroceryItem> getFrequentItems() {
    try {
      return groceryBox.values
          .where((item) => item.frequency >= AppConstants.frequentItemThreshold)
          .toList()
        ..sort((a, b) => b.frequency.compareTo(a.frequency));
    } catch (e) {
      debugPrint('Error getting frequent items: $e');
      return [];
    }
  }

  /// Clear all items (for testing or reset functionality)
  Future<void> clearAllItems() async {
    try {
      await groceryBox.clear();
    } catch (e) {
      debugPrint('Error clearing all items: $e');
      rethrow;
    }
  }

  /// Get items count
  int getItemsCount() {
    try {
      return groceryBox.length;
    } catch (e) {
      debugPrint('Error getting items count: $e');
      return 0;
    }
  }

  /// Get checked items count
  int getCheckedItemsCount() {
    try {
      return groceryBox.values.where((item) => item.isChecked).length;
    } catch (e) {
      debugPrint('Error getting checked items count: $e');
      return 0;
    }
  }

  /// Close the storage service
  Future<void> close() async {
    try {
      await _groceryBox?.close();
      _groceryBox = null;
    } catch (e) {
      debugPrint('Error closing storage service: $e');
    }
  }

  /// Check if storage is initialized
  bool get isInitialized => _groceryBox != null && _groceryBox!.isOpen;
}
