import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/grocery_provider.dart';
import '../providers/theme_provider.dart';
import '../services/sample_data_service.dart';
import '../services/theme_service.dart';
import '../utils/constants.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // App Information Section
          _buildSectionHeader('App Information'),
          _buildInfoCard(context),
          const SizedBox(height: AppConstants.largePadding),

          // Data Management Section
          _buildSectionHeader('Data Management'),
          _buildDataManagementCard(context),
          const SizedBox(height: AppConstants.largePadding),

          // Preferences Section
          _buildSectionHeader('Preferences'),
          _buildPreferencesCard(context),
          const SizedBox(height: AppConstants.largePadding),

          // About Section
          _buildSectionHeader('About'),
          _buildAboutCard(context),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Consumer<GroceryProvider>(
          builder: (context, provider, child) {
            return Column(
              children: [
                _buildStatRow('Total Items', provider.itemsCount.toString()),
                _buildStatRow('Checked Items', provider.checkedItemsCount.toString()),
                _buildStatRow('Frequent Items', provider.frequentItems.length.toString()),
                const Divider(),
                Row(
                  children: [
                    Icon(Icons.info_outline, color: AppConstants.primaryColor),
                    const SizedBox(width: AppConstants.smallPadding),
                    const Expanded(
                      child: Text(
                        'Statistics are updated in real-time as you use the app.',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppConstants.secondaryTextColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDataManagementCard(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(Icons.clear_all, color: AppConstants.primaryColor),
            title: const Text('Clear Checked Items'),
            subtitle: const Text('Remove all completed items from your list'),
            onTap: () => _showClearCheckedDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.delete_sweep, color: Colors.red),
            title: const Text('Clear All Items'),
            subtitle: const Text('Remove all items from your list'),
            onTap: () => _showClearAllDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.refresh, color: AppConstants.primaryColor),
            title: const Text('Reset Sample Data'),
            subtitle: const Text('Restore original sample items (for testing)'),
            onTap: () => _showResetSampleDataDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferencesCard(BuildContext context) {
    return Card(
      child: Column(
        children: [
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return ListTile(
                leading: Icon(
                  themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                  color: AppConstants.primaryColor,
                ),
                title: const Text('Theme Mode'),
                subtitle: Text(_getThemeModeText(themeProvider.themeMode)),
                trailing: Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () => _showThemeDialog(context),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.palette, color: AppConstants.primaryColor),
            title: const Text('App Color'),
            subtitle: const Text('Customize app appearance'),
            trailing: Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: themeProvider.primaryColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                );
              },
            ),
            onTap: () => _showColorDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.notifications, color: AppConstants.primaryColor),
            title: const Text('Notifications'),
            subtitle: const Text('Shopping reminders and updates'),
            trailing: Switch(
              value: true,
              onChanged: (value) {
                // TODO: Implement notification settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Notification settings coming soon!'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.sort, color: AppConstants.primaryColor),
            title: const Text('Default Sort Order'),
            subtitle: const Text('Unchecked first, then alphabetical'),
            trailing: Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // TODO: Implement sort preferences
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Sort preferences coming soon!'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAboutCard(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(Icons.info, color: AppConstants.primaryColor),
            title: const Text('Version'),
            subtitle: Text(AppConstants.appVersion),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.code, color: AppConstants.primaryColor),
            title: const Text('Built with Flutter'),
            subtitle: const Text('Made with ❤️ for grocery shopping'),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.star, color: AppConstants.primaryColor),
            title: const Text('Rate App'),
            subtitle: const Text('Help us improve GroceryGo'),
            trailing: Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // TODO: Implement app rating
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showClearCheckedDialog(BuildContext context) {
    final provider = context.read<GroceryProvider>();
    if (provider.checkedItemsCount == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No checked items to clear'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Checked Items'),
        content: Text(
          'Are you sure you want to remove all ${provider.checkedItemsCount} checked items?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              provider.clearCheckedItems();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Checked items cleared'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    final provider = context.read<GroceryProvider>();
    if (provider.itemsCount == 0) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Items'),
        content: Text(
          'Are you sure you want to remove all ${provider.itemsCount} items? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              provider.clearAllItems();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All items cleared'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _showResetSampleDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Sample Data'),
        content: const Text(
          'This will clear all current items and restore the original sample data. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final provider = context.read<GroceryProvider>();
              final messenger = ScaffoldMessenger.of(context);
              
              await SampleDataService.instance.resetSampleData();
              await SampleDataService.instance.forceAddSampleData();
              await provider.refresh();
              
              navigator.pop();
              messenger.showSnackBar(
                const SnackBar(
                  content: Text('Sample data restored'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Light mode';
      case ThemeMode.dark:
        return 'Dark mode';
      case ThemeMode.system:
        return 'Follow system';
    }
  }

  void _showThemeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<ThemeMode>(
                  title: const Text('Light'),
                  subtitle: const Text('Always use light theme'),
                  value: ThemeMode.light,
                  groupValue: themeProvider.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      themeProvider.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
                RadioListTile<ThemeMode>(
                  title: const Text('Dark'),
                  subtitle: const Text('Always use dark theme'),
                  value: ThemeMode.dark,
                  groupValue: themeProvider.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      themeProvider.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
                RadioListTile<ThemeMode>(
                  title: const Text('System'),
                  subtitle: const Text('Follow system setting'),
                  value: ThemeMode.system,
                  groupValue: themeProvider.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      themeProvider.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showColorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose App Color'),
        content: Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            return SizedBox(
              width: double.maxFinite,
              child: GridView.builder(
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: ThemeService.availableColors.length,
                itemBuilder: (context, index) {
                  final color = ThemeService.availableColors[index];
                  final isSelected = color == themeProvider.primaryColor;

                  return GestureDetector(
                    onTap: () {
                      themeProvider.setPrimaryColor(color);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                        border: isSelected
                            ? Border.all(color: Colors.white, width: 3)
                            : null,
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: color.withValues(alpha: 0.5),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ]
                            : null,
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 24,
                            )
                          : null,
                    ),
                  );
                },
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
