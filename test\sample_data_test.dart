import 'package:flutter_test/flutter_test.dart';
import 'package:grocerygo/services/sample_data_service.dart';

void main() {
  group('SampleDataService Tests', () {
    late SampleDataService sampleDataService;

    setUp(() {
      sampleDataService = SampleDataService.instance;
    });

    test('should provide sample data statistics', () {
      final stats = sampleDataService.getSampleDataStats();
      
      expect(stats['total'], greaterThan(0));
      expect(stats['checked'], greaterThanOrEqualTo(0));
      expect(stats['unchecked'], greaterThanOrEqualTo(0));
      expect(stats['frequent'], greaterThanOrEqualTo(0));
      
      // Total should equal checked + unchecked
      expect(stats['total'], equals(stats['checked']! + stats['unchecked']!));
    });

    test('should provide welcome message', () {
      final message = sampleDataService.getWelcomeMessage();
      
      expect(message, isNotEmpty);
      expect(message, contains('Welcome'));
      expect(message, contains('sample'));
    });

    test('sample data should have realistic items', () {
      final stats = sampleDataService.getSampleDataStats();
      
      // Should have between 5-10 items as specified
      expect(stats['total'], greaterThanOrEqualTo(5));
      expect(stats['total'], lessThanOrEqualTo(10));
      
      // Should have some frequent items
      expect(stats['frequent'], greaterThan(0));
      
      // Should have mix of checked and unchecked
      expect(stats['checked'], greaterThan(0));
      expect(stats['unchecked'], greaterThan(0));
    });
  });
}
