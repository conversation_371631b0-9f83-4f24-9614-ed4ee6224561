import 'package:hive/hive.dart';

part 'grocery_item.g.dart';

@HiveType(typeId: 0)
class GroceryItem extends HiveObject {
  @HiveField(0)
  String name;

  @HiveField(1)
  bool isChecked;

  @HiveField(2)
  int frequency;

  @HiveField(3)
  DateTime createdAt;

  GroceryItem({
    required this.name,
    this.isChecked = false,
    this.frequency = 1,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  // Copy constructor for creating a new instance
  GroceryItem copyWith({
    String? name,
    bool? isChecked,
    int? frequency,
    DateTime? createdAt,
  }) {
    return GroceryItem(
      name: name ?? this.name,
      isChecked: isChecked ?? this.isChecked,
      frequency: frequency ?? this.frequency,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Convert to Map for debugging
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'isChecked': isChecked,
      'frequency': frequency,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'GroceryItem(name: $name, isChecked: $isChecked, frequency: $frequency, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroceryItem && other.name.toLowerCase() == name.toLowerCase();
  }

  @override
  int get hashCode => name.toLowerCase().hashCode;
}
