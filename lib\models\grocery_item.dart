import 'package:hive/hive.dart';

part 'grocery_item.g.dart';

@HiveType(typeId: 0)
class GroceryItem extends HiveObject {
  @HiveField(0)
  String name;

  @HiveField(1)
  bool isChecked;

  @HiveField(2)
  int frequency;

  @HiveField(3)
  DateTime createdAt;

  @HiveField(4)
  int quantity;

  @HiveField(5)
  String unit;

  @HiveField(6)
  double? price;

  @HiveField(7)
  String category;

  @HiveField(8)
  String? notes;

  @HiveField(9)
  DateTime? lastPurchased;

  @HiveField(10)
  String? imageIcon;

  GroceryItem({
    required this.name,
    this.isChecked = false,
    this.frequency = 1,
    DateTime? createdAt,
    this.quantity = 1,
    this.unit = 'item',
    this.price,
    this.category = 'Other',
    this.notes,
    this.lastPurchased,
    this.imageIcon,
  }) : createdAt = createdAt ?? DateTime.now();

  // Copy constructor for creating a new instance
  GroceryItem copyWith({
    String? name,
    bool? isChecked,
    int? frequency,
    DateTime? createdAt,
    int? quantity,
    String? unit,
    double? price,
    String? category,
    String? notes,
    DateTime? lastPurchased,
    String? imageIcon,
  }) {
    return GroceryItem(
      name: name ?? this.name,
      isChecked: isChecked ?? this.isChecked,
      frequency: frequency ?? this.frequency,
      createdAt: createdAt ?? this.createdAt,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      price: price ?? this.price,
      category: category ?? this.category,
      notes: notes ?? this.notes,
      lastPurchased: lastPurchased ?? this.lastPurchased,
      imageIcon: imageIcon ?? this.imageIcon,
    );
  }

  // Convert to Map for debugging
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'isChecked': isChecked,
      'frequency': frequency,
      'createdAt': createdAt.toIso8601String(),
      'quantity': quantity,
      'unit': unit,
      'price': price,
      'category': category,
      'notes': notes,
      'lastPurchased': lastPurchased?.toIso8601String(),
      'imageIcon': imageIcon,
    };
  }

  @override
  String toString() {
    return 'GroceryItem(name: $name, quantity: $quantity $unit, category: $category, isChecked: $isChecked, frequency: $frequency)';
  }

  // Get formatted quantity display
  String get quantityDisplay {
    if (quantity == 1 && unit == 'item') {
      return name;
    }
    return '$quantity $unit $name';
  }

  // Get formatted price display
  String get priceDisplay {
    if (price == null) return '';
    return '\$${price!.toStringAsFixed(2)}';
  }

  // Get total price for quantity
  double? get totalPrice {
    if (price == null) return null;
    return price! * quantity;
  }

  // Get formatted total price display
  String get totalPriceDisplay {
    final total = totalPrice;
    if (total == null) return '';
    return '\$${total.toStringAsFixed(2)}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroceryItem && other.name.toLowerCase() == name.toLowerCase();
  }

  @override
  int get hashCode => name.toLowerCase().hashCode;
}
