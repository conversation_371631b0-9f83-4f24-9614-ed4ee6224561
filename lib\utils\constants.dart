import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'GroceryGo';
  static const String appVersion = '1.0.0';

  // Hive Box Names
  static const String groceryBoxName = 'grocery_items';

  // Frequency threshold for frequent items
  static const int frequentItemThreshold = 2;

  // Colors
  static const MaterialColor primarySwatch = Colors.green;
  static const Color primaryColor = Color(0xFF4CAF50);
  static const Color accentColor = Color(0xFF8BC34A);
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardColor = Colors.white;
  static const Color textColor = Color(0xFF212121);
  static const Color secondaryTextColor = Color(0xFF757575);
  static const Color checkedItemColor = Color(0xFF9E9E9E);

  // Text Styles
  static const TextStyle titleTextStyle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: textColor,
  );

  static const TextStyle subtitleTextStyle = TextStyle(
    fontSize: 16,
    color: secondaryTextColor,
  );

  static const TextStyle bodyTextStyle = TextStyle(
    fontSize: 14,
    color: textColor,
  );

  static const TextStyle checkedTextStyle = TextStyle(
    fontSize: 14,
    color: checkedItemColor,
    decoration: TextDecoration.lineThrough,
  );

  // Spacing
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Messages
  static const String emptyListMessage = 'Your grocery list is empty.\nTap + to add items!';
  static const String noFrequentItemsMessage = 'No frequently bought items yet.\nAdd items to your list to see them here!';
  static const String itemAddedMessage = 'Item added to your list';
  static const String itemRemovedMessage = 'Item removed from list';
  static const String confirmDeleteTitle = 'Delete Item';
  static const String confirmDeleteMessage = 'Are you sure you want to delete this item?';

  // Input Validation
  static const int maxItemNameLength = 50;
  static const String emptyItemNameError = 'Please enter an item name';
  static const String itemNameTooLongError = 'Item name is too long';

  // Navigation
  static const List<String> bottomNavLabels = ['Home', 'Categories', 'Frequent', 'Settings'];

  // Units
  static const List<String> commonUnits = [
    'item', 'lbs', 'kg', 'oz', 'g', 'dozen', 'pack', 'bottle', 'can', 'box', 'bag'
  ];

  // Shopping
  static const double maxPrice = 999.99;
  static const int maxQuantity = 99;

  // Categories
  static const String defaultCategoryId = 'other';

  // Theme
  static const String lightThemeKey = 'light_theme';
  static const String darkThemeKey = 'dark_theme';
  static const String themePreferenceKey = 'theme_preference';

  // Analytics
  static const int maxShoppingHistoryDays = 365;
  static const int maxFrequentItemsDisplay = 20;
}
